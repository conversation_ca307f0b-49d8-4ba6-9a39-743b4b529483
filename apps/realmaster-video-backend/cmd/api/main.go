package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/real-rm/goauth/controller"
	"github.com/real-rm/goauth/middleware"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/advertiser"
	"realmaster-video-backend/internal/domain/category"
	"realmaster-video-backend/internal/domain/interaction"
	"realmaster-video-backend/internal/domain/main_property"
	"realmaster-video-backend/internal/domain/public"
	"realmaster-video-backend/internal/domain/state"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"
	"realmaster-video-backend/internal/server"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		panic(fmt.Sprintf("加载配置失败: %v", err))
	}

	// 2. 初始化日志
	if _, err := logger.NewLogger(); err != nil {
		panic(fmt.Sprintf("初始化日志失败: %v", err))
	}
	defer logger.Sync()

	// 3. 初始化数据库连接
	err = database.InitMongoDB()
	if err != nil {
		logger.Log.Fatal("初始化gomongo失败", logger.Error(err))
	}
	// gomongo会自动管理连接，无需手动关闭

	// 4. 初始化 JWT 认证
	if err := middleware.InitJWTSecret(); err != nil {
		logger.Log.Fatal("初始化JWT密钥失败", logger.Error(err))
	}

	// 创建索引（如果需要的话）
	// database.CreateIndexes(context.Background(), mongoClient.DB())

	// 5. 初始化各领域组件
	// 视频（提前以便注入）
	videoRepo := video.NewRepository()

	// 分类
	categoryRepo := category.NewRepository()
	categoryService := category.NewService(categoryRepo, videoRepo)

	// 确保 "None" 分类存在
	if err := categoryService.EnsureNoneCategoryExists(context.Background()); err != nil {
		logger.Log.Fatal("初始化 'None' 分类失败", logger.Error(err))
	}

	categoryHandler := category.NewHandler(categoryService)

	// 广告主
	advertiserRepo := advertiser.NewRepository()
	advertiserService := advertiser.NewService(advertiserRepo, videoRepo, cfg)
	advertiserHandler := advertiser.NewHandler(advertiserService, cfg)

	// 房源
	propertyRepo := main_property.NewRepository()
	propertyService := main_property.NewService(propertyRepo)
	propertyHandler := main_property.NewHandler(propertyService)

	// 视频
	videoService := video.NewService(videoRepo, cfg)
	videoHandler, err := video.NewHandler(videoService, cfg.Server.DraftDir, cfg)
	if err != nil {
		logger.Log.Fatal("创建视频处理器失败", logger.Error(err))
	}

	// 注意：移除了内部文件上传服务，现在完全使用goupload

	// 事务管理器
	txManager := database.NewTransactionManager("realmaster_video", cfg.Transaction.Support)

	// 交互事件
	interactionRepo := interaction.NewRepository()
	stateRepo := state.NewRepository()
	interactionService := interaction.NewService(interactionRepo, stateRepo, videoRepo, txManager)
	interactionHandler := interaction.NewHandler(interactionService)

	// 公共API
	publicService := public.NewService(videoRepo, stateRepo, cfg)
	publicHandler := public.NewHandler(publicService)

	// 认证处理器（使用 goauth 的 controller）

	// 6. 创建 Gin 引擎
	router := gin.Default()

	// 使用安全的CORS中间件配置
	corsConfig := cors.Config{
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}

	// 根据环境设置不同的CORS策略
	if gin.Mode() == gin.DebugMode {
		// 开发环境：允许本地开发域名和Tailscale远程访问
		corsConfig.AllowOrigins = []string{
			"http://localhost:3000",
			"http://localhost:5173", // Vite 默认端口
			"http://localhost:8080",
			"http://127.0.0.1:3000",
			"http://127.0.0.1:5173", // Vite 默认端口
			"http://127.0.0.1:8080",
			// ORBStack虚拟机IP访问支持
			"http://**************:3000",
			"http://**************:5173",
			"http://**************:8080",
			// Tailscale 远程访问支持 (Mac主机的Tailscale IP)
			"http://*************:5173", // 你朋友的前端地址
			"http://*************:3000",
			"http://*************:8080",
		}
	} else {
		// 生产环境：只允许特定域名
		corsConfig.AllowOrigins = []string{
			"https://yourdomain.com",
			"https://www.yourdomain.com",
			// 添加你的实际生产域名
		}
	}

	router.Use(cors.New(corsConfig))

	// 注意：媒体文件现在通过nginx提供，不再需要Go后端的静态文件服务

	// 配置临时文件服务，用于访问临时上传的文件
	// 临时文件也使用L1/L2分层存储
	// 例如，/temp/USER/2025-28/abc123/thumb-xxx
	router.Static("/draft", cfg.Server.DraftDir)

	// 7. 注册 goauth 认证路由
	controller.Auth(router.Group("/auth"))

	// 8. 开发环境专用路由
	if gin.Mode() == gin.DebugMode {
		devGroup := router.Group("/dev")
		{
			devGroup.GET("/jwt", func(c *gin.Context) {
				// 支持通过查询参数选择用户
				userParam := c.Query("user")

				var userID string
				var roles []string

				switch userParam {
				case "admin":
					userID = "dev_admin_001"
					roles = []string{"user", "admin"}
				case "user":
					userID = "dev_user_002"
					roles = []string{"user"}
				case "realtor":
					userID = "dev_realtor_003"
					roles = []string{"user", "realtor"}
				default:
					userID = "dev_user_123"
					roles = []string{"user", "admin"}
				}

				// 使用 goauth 的方式生成 JWT
				now := time.Now()
				expiresAt := now.Add(24 * time.Hour).Unix()

				// 创建 JWT claims
				claims := jwt.MapClaims{
					"sub":   userID,
					"roles": roles,
					"exp":   expiresAt,
					"iat":   now.Unix(),
				}

				token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				jwtToken, err := token.SignedString([]byte(middleware.GetSecretKey()))
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": "生成JWT失败"})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"jwt":        jwtToken,
					"expires_in": 24 * 3600, // 24小时，以秒为单位
					"user_id":    userID,
					"roles":      roles,
				})
			})

		}
	}

	// 8. 注册受保护的管理后台路由
	adminGroup := router.Group("/video/admin")
	adminGroup.Use(middleware.JWTAuthMiddleware()) // 使用 goauth 的 JWT 中间件
	{
		server.RegisterCategoryRoutes(adminGroup, categoryHandler)
		server.RegisterAdvertiserRoutes(adminGroup, advertiserHandler)
		server.RegisterMainPropertyRoutes(adminGroup, propertyHandler)
		server.RegisterVideoRoutes(adminGroup, videoHandler)
	}

	// 9. 注册公共API路由（需要JWT保护）
	publicGroup := router.Group("/video/public")
	publicGroup.Use(middleware.JWTAuthMiddleware()) // 使用 goauth 的 JWT 中间件
	{
		// Feed接口
		publicGroup.GET("/feed", publicHandler.GetFeed)

		// 用户状态接口
		publicGroup.POST("/states/batch", publicHandler.GetUserStates)
		publicGroup.GET("/states", publicHandler.GetUserStatesQuery)

		// 用户收藏接口
		publicGroup.GET("/favorites", publicHandler.GetUserFavorites)

		// 交互事件接口
		publicGroup.POST("/interactions", interactionHandler.CreateInteraction)
		publicGroup.GET("/interactions/user", interactionHandler.GetUserInteractions)
		publicGroup.GET("/interactions/video/:videoId", interactionHandler.GetVideoInteractions)
	}

	// 注意：移除了内部文件上传路由，现在完全使用goupload

	// 8. 启动服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler: router,
	}

	// 优雅关闭
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Log.Fatal("启动服务器失败", logger.Error(err))
		}
	}()

	logger.Log.Info("服务器已启动",
		logger.String("host", cfg.Server.Host),
		logger.Int("port", cfg.Server.Port),
	)

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Log.Info("正在关闭服务器...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Log.Fatal("服务器关闭失败", logger.Error(err))
	}

	logger.Log.Info("服务器已关闭")
}
