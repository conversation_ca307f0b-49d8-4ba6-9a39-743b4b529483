# gomongo数据库配置
[dbs]
verbose = 1  # 日志详细级别

[dbs.realmaster_video]
uri = "mongodb://************:27017/realmaster_video?replicaSet=rs0"

# 应用程序配置
[server]
port = 8080
host = "0.0.0.0"  # 保持0.0.0.0以允许所有接口访问
draft_dir = "/var/www/draft/rm_video_uploads"

# golog日志配置
[golog]
dir = "./logs"                      # 日志目录（项目根目录下的logs文件夹）
level = "info"                       # 全局日志级别 (debug, verbose, info, warn, error)
verbose = "verbose.log"              # Verbose日志文件
info = "info.log"                   # Info日志文件
error = "error.log"                 # Error日志文件
format = "json"                     # 日志格式 (json或text)

[media]
server_url = "http://localhost:3000"

# JWT 认证配置
[auth]
jwtSecret = "test-video-backend-secret-key-for-development"

# 用户数据库配置（goauth 需要）
[dbs.data]
uri = "mongodb://************:27017/realmaster_video?replicaSet=rs0"  # 开发阶段使用同一个数据库

# 事务支持配置
[transaction]
support = true  # 副本集MongoDB支持事务，设置为true

# goupload 文件上传配置
[userupload]
site = "TEST"
  # 草稿阶段 - 用户上传的原始文件
  [[userupload.types]]
    entryName = "video_draft"
    prefix = "/draft/videos"
    tmpPath = "/tmp/goupload_temp"
    maxSize = "2GB"
    storage = [
      { type = "local", path = "/var/www/draft/rm_video_drafts" }
    ]
  [[userupload.types]]
    entryName = "thumbnail_draft"
    prefix = "/draft/thumbnails"
    tmpPath = "/tmp/goupload_temp"
    maxSize = "50MB"
    storage = [
      { type = "local", path = "/var/www/draft/rm_thumbnail_drafts" }
    ]

  # 最终阶段 - Worker处理后的文件
  [[userupload.types]]
    entryName = "video_final"
    prefix = "/media/videos"
    tmpPath = "/tmp/goupload_temp"
    maxSize = "10GB"
    storage = [
      { type = "local", path = "/var/www/media/rm_videos" }
    ]
  [[userupload.types]]
    entryName = "thumbnail_final"
    prefix = "/media/thumbnails"
    tmpPath = "/tmp/goupload_temp"
    maxSize = "50MB"
    storage = [
      { type = "local", path = "/var/www/media/rm_thumbnails" }
    ]

  # Client头像上传
  [[userupload.types]]
    entryName = "client_avatar"
    prefix = "/media/avatars"
    tmpPath = "/tmp/goupload_temp"
    maxSize = "10MB"
    storage = [
      { type = "local", path = "/var/www/media/rm_avatars" }
    ]
